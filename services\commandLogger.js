const { WebhookClient, EmbedBuilder } = require('discord.js');
const chalk = require('chalk');

class CommandLogger {
    constructor() {
        // Command logging webhook - You need to create a webhook in channel 1386343803773915210
        // and replace this URL with the actual webhook URL
        this.webhookUrl = process.env.COMMAND_LOG_WEBHOOK_URL || null;
        this.webhook = null;

        if (this.webhookUrl) {
            this.initializeWebhook();
        } else {
            console.log(chalk.yellow('⚠️ COMMAND_LOG_WEBHOOK_URL not set in environment variables'));
        }
    }

    initializeWebhook() {
        try {
            // Extract webhook ID and token from URL
            const webhookMatch = this.webhookUrl.match(/webhooks\/(\d+)\/([^\/]+)/);
            if (webhookMatch) {
                const [, webhookId, webhookToken] = webhookMatch;
                this.webhook = new WebhookClient({ id: webhookId, token: webhookToken });
                console.log(chalk.green('✅ Command logging webhook initialized'));
            } else {
                console.log(chalk.yellow('⚠️ Invalid webhook URL format for command logging'));
            }
        } catch (error) {
            console.error(chalk.red('❌ Failed to initialize command logging webhook:'), error);
        }
    }

    async logSlashCommand(interaction) {
        if (!this.webhook) return;

        try {
            const { user, guild, commandName, options } = interaction;
            
            // Get command options as a readable string
            const optionsString = this.formatCommandOptions(options);
            
            // Get user info
            const member = guild ? await guild.members.fetch(user.id).catch(() => null) : null;
            const userRoles = member ? member.roles.cache
                .filter(role => role.name !== '@everyone')
                .map(role => role.name)
                .slice(0, 5) // Limit to 5 roles
                .join(', ') : 'N/A';

            // Create detailed embed
            const embed = new EmbedBuilder()
                .setColor('#5865F2') // Discord blurple for slash commands
                .setTitle('🔹 Slash Command Used')
                .setDescription(`\`/${commandName}${optionsString ? ` ${optionsString}` : ''}\``)
                .addFields([
                    {
                        name: '👤 User Information',
                        value: [
                            `**User:** ${user.tag} (${user.id})`,
                            `**Display Name:** ${member?.displayName || user.displayName || user.username}`,
                            `**Account Created:** <t:${Math.floor(user.createdTimestamp / 1000)}:R>`,
                            `**Top Roles:** ${userRoles || 'None'}`
                        ].join('\n'),
                        inline: true
                    },
                    {
                        name: '🏠 Server Information',
                        value: guild ? [
                            `**Server:** ${guild.name}`,
                            `**Server ID:** ${guild.id}`,
                            `**Members:** ${guild.memberCount.toLocaleString()}`,
                            `**Owner:** <@${guild.ownerId}>`,
                            `**Created:** <t:${Math.floor(guild.createdTimestamp / 1000)}:R>`
                        ].join('\n') : 'DM Command',
                        inline: true
                    },
                    {
                        name: '📊 Command Details',
                        value: [
                            `**Command:** \`/${commandName}\``,
                            `**Channel:** ${interaction.channel ? `<#${interaction.channel.id}>` : 'DM'}`,
                            `**Timestamp:** <t:${Math.floor(Date.now() / 1000)}:F>`,
                            `**Options:** ${optionsString || 'None'}`
                        ].join('\n'),
                        inline: false
                    }
                ])
                .setThumbnail(user.displayAvatarURL({ dynamic: true, size: 128 }))
                .setFooter({ 
                    text: guild ? `${guild.name} • ${guild.memberCount} members` : 'Direct Message',
                    iconURL: guild?.iconURL({ dynamic: true, size: 64 }) || null
                })
                .setTimestamp();

            await this.webhook.send({ embeds: [embed] });

        } catch (error) {
            console.error(chalk.red('❌ Failed to log slash command:'), error);
        }
    }

    async logMessageCommand(message, commandName, args) {
        if (!this.webhook) return;

        try {
            const { author, guild, channel } = message;
            
            // Get user info
            const member = guild ? await guild.members.fetch(author.id).catch(() => null) : null;
            const userRoles = member ? member.roles.cache
                .filter(role => role.name !== '@everyone')
                .map(role => role.name)
                .slice(0, 5) // Limit to 5 roles
                .join(', ') : 'N/A';

            // Format command with args
            const fullCommand = `${commandName}${args.length > 0 ? ` ${args.join(' ')}` : ''}`;

            // Create detailed embed
            const embed = new EmbedBuilder()
                .setColor('#FEE75C') // Yellow for message commands
                .setTitle('💬 Message Command Used')
                .setDescription(`\`${fullCommand}\``)
                .addFields([
                    {
                        name: '👤 User Information',
                        value: [
                            `**User:** ${author.tag} (${author.id})`,
                            `**Display Name:** ${member?.displayName || author.displayName || author.username}`,
                            `**Account Created:** <t:${Math.floor(author.createdTimestamp / 1000)}:R>`,
                            `**Top Roles:** ${userRoles || 'None'}`
                        ].join('\n'),
                        inline: true
                    },
                    {
                        name: '🏠 Server Information',
                        value: guild ? [
                            `**Server:** ${guild.name}`,
                            `**Server ID:** ${guild.id}`,
                            `**Members:** ${guild.memberCount.toLocaleString()}`,
                            `**Owner:** <@${guild.ownerId}>`,
                            `**Created:** <t:${Math.floor(guild.createdTimestamp / 1000)}:R>`
                        ].join('\n') : 'DM Command',
                        inline: true
                    },
                    {
                        name: '📊 Command Details',
                        value: [
                            `**Command:** \`${commandName}\``,
                            `**Arguments:** ${args.length > 0 ? `\`${args.join(' ')}\`` : 'None'}`,
                            `**Channel:** ${channel ? `<#${channel.id}>` : 'DM'}`,
                            `**Message ID:** ${message.id}`,
                            `**Timestamp:** <t:${Math.floor(message.createdTimestamp / 1000)}:F>`
                        ].join('\n'),
                        inline: false
                    }
                ])
                .setThumbnail(author.displayAvatarURL({ dynamic: true, size: 128 }))
                .setFooter({ 
                    text: guild ? `${guild.name} • ${guild.memberCount} members` : 'Direct Message',
                    iconURL: guild?.iconURL({ dynamic: true, size: 64 }) || null
                })
                .setTimestamp();

            await this.webhook.send({ embeds: [embed] });

        } catch (error) {
            console.error(chalk.red('❌ Failed to log message command:'), error);
        }
    }

    formatCommandOptions(options) {
        if (!options || options.data.length === 0) return '';
        
        return options.data.map(option => {
            let value = option.value;
            
            // Handle different option types
            switch (option.type) {
                case 6: // USER
                    value = `<@${value}>`;
                    break;
                case 7: // CHANNEL
                    value = `<#${value}>`;
                    break;
                case 8: // ROLE
                    value = `<@&${value}>`;
                    break;
                case 11: // ATTACHMENT
                    value = '[Attachment]';
                    break;
                default:
                    // For strings, truncate if too long
                    if (typeof value === 'string' && value.length > 50) {
                        value = value.substring(0, 47) + '...';
                    }
            }
            
            return `${option.name}:${value}`;
        }).join(' ');
    }

    async logError(error, context = {}) {
        if (!this.webhook) return;

        try {
            const embed = new EmbedBuilder()
                .setColor('#ED4245') // Red for errors
                .setTitle('❌ Command Error')
                .setDescription(`\`\`\`js\n${error.message}\`\`\``)
                .addFields([
                    {
                        name: '📍 Error Context',
                        value: [
                            `**Command:** ${context.commandName || 'Unknown'}`,
                            `**User:** ${context.user?.tag || 'Unknown'}`,
                            `**Server:** ${context.guild?.name || 'Unknown'}`,
                            `**Timestamp:** <t:${Math.floor(Date.now() / 1000)}:F>`
                        ].join('\n'),
                        inline: false
                    }
                ])
                .setTimestamp();

            if (error.stack) {
                embed.addFields([
                    {
                        name: '🔍 Stack Trace',
                        value: `\`\`\`js\n${error.stack.substring(0, 1000)}\`\`\``,
                        inline: false
                    }
                ]);
            }

            await this.webhook.send({ embeds: [embed] });

        } catch (logError) {
            console.error(chalk.red('❌ Failed to log error:'), logError);
        }
    }

    // Method to update webhook URL if needed
    updateWebhookUrl(newUrl) {
        this.webhookUrl = newUrl;
        this.initializeWebhook();
    }
}

// Export singleton instance
module.exports = new CommandLogger();
