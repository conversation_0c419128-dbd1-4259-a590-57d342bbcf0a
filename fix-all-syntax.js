const fs = require('fs');
const path = require('path');

const setupFilePath = path.join(__dirname, 'commands', 'slashCommands', 'info', 'setup.js');

// Read the file
let content = fs.readFileSync(setupFilePath, 'utf8');

console.log('🔧 Fixing all syntax errors in setup.js...');

// Fix 1: Missing commas in addChoices
content = content.replace(
    /{ name: '([^']+)' value: '([^']+)' }/g,
    "{ name: '$1', value: '$2' }"
);

// Fix 2: Missing commas in object properties
content = content.replace(
    /name: '([^']+)'\s*value: /g,
    "name: '$1',\n                    value: "
);

content = content.replace(
    /value: ([^,\n]+)\s*inline: /g,
    "value: $1,\n                    inline: "
);

// Fix 3: Missing commas in role creation arrays
content = content.replace(
    /{ name: '([^']+)' key: '([^']+)' type: '([^']+)' color: ([^}]+) }/g,
    "{ name: '$1', key: '$2', type: '$3', color: $4 }"
);

// Fix 4: Missing commas in array elements
content = content.replace(
    /'([^']+)' '([^']+)'/g,
    "'$1', '$2'"
);

// Fix 5: Fix specific patterns that might be broken
content = content.replace(
    /return \['([^']+)' '([^']+)'/g,
    "return ['$1', '$2'"
);

// Fix 6: Fix double commas
content = content.replace(
    /,,/g,
    ','
);

// Fix 7: Fix missing commas in embed fields
content = content.replace(
    /inline: (true|false)\s*}\s*{/g,
    "inline: $1\n                },\n                {"
);

// Fix 8: Fix missing commas in button arrays
content = content.replace(
    /\.setStyle\([^)]+\)\s*new ButtonBuilder/g,
    (match) => match.replace('new ButtonBuilder', ',\n                new ButtonBuilder')
);

// Fix 9: Fix missing commas in ActionRowBuilder components
content = content.replace(
    /\.addComponents\(\s*new ButtonBuilder[^)]+\)\s*new ButtonBuilder/g,
    (match) => match.replace(/\)\s*new ButtonBuilder/, '),\n                new ButtonBuilder')
);

// Write the fixed content back to the file
fs.writeFileSync(setupFilePath, content, 'utf8');

console.log('✅ Applied syntax fixes to setup.js');

// Test the syntax
try {
    require(setupFilePath);
    console.log('✅ setup.js syntax is now valid!');
} catch (error) {
    console.log('❌ Still has syntax errors:');
    console.log(error.message);
    
    // Try to identify the line with the error
    const lines = content.split('\n');
    const errorMatch = error.message.match(/line (\d+)/);
    if (errorMatch) {
        const lineNum = parseInt(errorMatch[1]);
        console.log(`Line ${lineNum}: ${lines[lineNum - 1]}`);
    }
}