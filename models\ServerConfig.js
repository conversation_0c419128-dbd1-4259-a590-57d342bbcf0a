const mongoose = require('mongoose');

// Schema for dungeon role configuration
const DungeonRolesSchema = new mongoose.Schema({
    E: { type: String, default: null },
    D: { type: String, default: null },
    C: { type: String, default: null },
    B: { type: String, default: null },
    A: { type: String, default: null },
    S: { type: String, default: null },
    SS: { type: String, default: null },
    G: { type: String, default: null },
    N: { type: String, default: null },
    DUNGEON_PING: { type: String, default: null },
    RED_DUNGEON: { type: String, default: null },
    DOUBLE_DUNGEON: { type: String, default: null }
}, { _id: false });

// Schema for world roles configuration
const WorldRolesSchema = new mongoose.Schema({
    1: { type: String, default: null }, // World 1 ping role
    2: { type: String, default: null }  // World 2 ping role
}, { _id: false });

// Schema for island roles configuration
const IslandRolesSchema = new mongoose.Schema({
    'Leveling City': { type: String, default: null },
    'Grass Village': { type: String, default: null },
    'Brum Island': { type: String, default: null },
    'Faceheal Town': { type: String, default: null },
    'Lucky Kingdom': { type: String, default: null },
    'Nipon City': { type: String, default: null },
    'Mori Town': { type: String, default: null },
    'Dragon City': { type: String, default: null },
    'XZ City': { type: String, default: null },
    'Kindama City': { type: String, default: null },
    'Hunters City': { type: String, default: null },
    'Nen City': { type: String, default: null },
    "Hurricane Town": { type: String, default: null }
}, { _id: false });

// Schema for world boss roles configuration
const WorldBossRolesSchema = new mongoose.Schema({
    'Leveling City': { type: String, default: null },
    'Grass Village': { type: String, default: null },
    'Faceheal Town': { type: String, default: null },
    'Nipon City': { type: String, default: null },
    'Dragon City': { type: String, default: null },
    'Kindama City': { type: String, default: null },
    'Nen City': { type: String, default: null }
}, { _id: false });

// Schema for dungeon alert configuration
const DungeonAlertSchema = new mongoose.Schema({
    enabled: { type: Boolean, default: false },
    targetChannelId: { type: String, default: null },
    dungeonRoles: { type: DungeonRolesSchema, default: () => ({}) },
    worldRoles: { type: WorldRolesSchema, default: () => ({}) },
    islandRoles: { type: IslandRolesSchema, default: () => ({}) },
    // Role category enable/disable flags
    rankRolesEnabled: { type: Boolean, default: true },
    worldRolesEnabled: { type: Boolean, default: true },
    islandRolesEnabled: { type: Boolean, default: true },
    specialRolesEnabled: { type: Boolean, default: true }
}, { _id: false });

// Schema for world boss alert configuration
const WorldBossAlertSchema = new mongoose.Schema({
    enabled: { type: Boolean, default: false },
    targetChannelId: { type: String, default: null },
    roleId: { type: String, default: null }, // General world boss ping role
    worldBossRoles: { type: WorldBossRolesSchema, default: () => ({}) }
}, { _id: false });

// Schema for infernal alert configuration
const InfernalAlertSchema = new mongoose.Schema({
    enabled: { type: Boolean, default: false },
    targetChannelId: { type: String, default: null },
    generalAlertRole: { type: String, default: null },
    monarchAlertRole: { type: String, default: null },
    webhookName: { type: String, default: 'RankBreaker' },
    webhookAvatar: { type: String, default: 'https://cdn.discordapp.com/icons/1362356687092191442/25b53ae035c74c9d6edcf8ca11dfc205.webp?size=1024' },
    messageFormat: { type: String, enum: ['default', 'custom'], default: 'default' }
}, { _id: false });

// Main server configuration schema
const ServerConfigSchema = new mongoose.Schema({
    serverId: { 
        type: String, 
        required: true, 
        unique: true,
        index: true 
    },
    name: { 
        type: String, 
        required: true 
    },
    dungeonAlert: { 
        type: DungeonAlertSchema, 
        default: () => ({ enabled: false }) 
    },
    worldBossAlert: { 
        type: WorldBossAlertSchema, 
        default: () => ({ enabled: false }) 
    },
    infernalAlert: { 
        type: InfernalAlertSchema, 
        default: () => ({ enabled: false }) 
    },
    createdAt: { 
        type: Date, 
        default: Date.now 
    },
    updatedAt: { 
        type: Date, 
        default: Date.now 
    },
    lastActivity: { 
        type: Date, 
        default: Date.now 
    }
}, {
    timestamps: true, // Automatically manage createdAt and updatedAt
    collection: 'serverconfigs'
});

// Update the updatedAt field before saving
ServerConfigSchema.pre('save', function(next) {
    this.updatedAt = new Date();
    next();
});

// Static methods for common queries
ServerConfigSchema.statics.findByServerId = function(serverId) {
    return this.findOne({ serverId });
};

ServerConfigSchema.statics.getEnabledDungeonServers = function() {
    return this.find({ 'dungeonAlert.enabled': true }).select('serverId name dungeonAlert');
};

ServerConfigSchema.statics.getEnabledWorldBossServers = function() {
    return this.find({ 'worldBossAlert.enabled': true }).select('serverId name worldBossAlert');
};

ServerConfigSchema.statics.getEnabledInfernalServers = function() {
    return this.find({ 'infernalAlert.enabled': true }).select('serverId name infernalAlert');
};

// Instance methods
ServerConfigSchema.methods.updateLastActivity = function() {
    this.lastActivity = new Date();
    return this.save();
};

const ServerConfig = mongoose.model('ServerConfig', ServerConfigSchema);

module.exports = ServerConfig;


