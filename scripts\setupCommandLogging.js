const { Client, GatewayIntentBits } = require('discord.js');
const fs = require('fs');
const path = require('path');
const config = require('../config/config.js');
require('dotenv').config();

// Create a temporary client to set up the webhook
const client = new Client({
    intents: [
        GatewayIntentBits.Guilds,
        GatewayIntentBits.GuildMessages
    ]
});

async function setupCommandLogging() {
    try {
        console.log('🔧 Setting up command logging webhook...');
        
        await client.login(config.token);
        console.log('✅ Bot logged in successfully');
        
        // Get the target channel
        const channelId = '1386343803773915210';
        const channel = await client.channels.fetch(channelId);
        
        if (!channel) {
            console.error('❌ Could not find the target channel');
            return;
        }
        
        console.log(`📍 Found channel: #${channel.name} in ${channel.guild.name}`);
        
        // Check if webhook already exists
        const existingWebhooks = await channel.fetchWebhooks();
        let webhook = existingWebhooks.find(wh => wh.name === 'Command Logger');
        
        if (webhook) {
            console.log('✅ Command Logger webhook already exists');
        } else {
            // Create new webhook
            webhook = await channel.createWebhook({
                name: 'Command Logger',
                avatar: 'https://cdn.discordapp.com/emojis/742043142750388304.png', // Optional: bot avatar
                reason: 'Command logging system setup'
            });
            console.log('✅ Created new Command Logger webhook');
        }
        
        const webhookUrl = `https://discord.com/api/webhooks/${webhook.id}/${webhook.token}`;
        console.log('🔗 Webhook URL:', webhookUrl);
        
        // Update .env file
        const envPath = path.join(__dirname, '../.env');
        let envContent = '';
        
        if (fs.existsSync(envPath)) {
            envContent = fs.readFileSync(envPath, 'utf8');
        }
        
        // Check if COMMAND_LOG_WEBHOOK_URL already exists
        if (envContent.includes('COMMAND_LOG_WEBHOOK_URL=')) {
            // Replace existing line
            envContent = envContent.replace(
                /COMMAND_LOG_WEBHOOK_URL=.*/,
                `COMMAND_LOG_WEBHOOK_URL=${webhookUrl}`
            );
        } else {
            // Add new line
            envContent += `\nCOMMAND_LOG_WEBHOOK_URL=${webhookUrl}\n`;
        }
        
        fs.writeFileSync(envPath, envContent);
        console.log('✅ Updated .env file with webhook URL');
        
        console.log('\n🎉 Command logging setup complete!');
        console.log('📋 Next steps:');
        console.log('1. Restart your bot to load the new environment variable');
        console.log('2. All slash commands and message commands will now be logged');
        console.log(`3. Logs will appear in #${channel.name}`);
        
        // Test the webhook
        console.log('\n🧪 Testing webhook...');
        await webhook.send({
            content: '✅ **Command logging system is now active!**\n\nAll commands will be logged to this channel with detailed information.'
        });
        console.log('✅ Test message sent successfully');
        
    } catch (error) {
        console.error('❌ Error setting up command logging:', error);
    } finally {
        client.destroy();
        process.exit(0);
    }
}

// Run the setup
setupCommandLogging();
