# 📊 Command Logging System

A comprehensive logging system that tracks all slash commands and message commands used in your Discord bot, sending detailed logs to a specified channel via webhook.

## 🚀 Features

### ✅ **Comprehensive Logging**
- **Slash Commands** - All `/command` interactions
- **Message Commands** - All `!command` messages  
- **Error Logging** - Failed command executions
- **Detailed Context** - User info, server info, command details

### 📋 **Logged Information**

#### 👤 User Information
- Username and user ID
- Display name in server
- Account creation date
- Top 5 roles in server

#### 🏠 Server Information  
- Server name and ID
- Member count
- Server owner
- Server creation date
- Server icon

#### 📊 Command Details
- Command name and full syntax
- Command arguments/options
- Channel where used
- Timestamp
- Message ID (for message commands)

### 🎨 **Visual Design**
- **Slash Commands**: Blue embeds with Discord blurple color
- **Message Commands**: Yellow embeds for easy distinction
- **Errors**: Red embeds with stack traces
- **Rich Thumbnails**: User avatars and server icons

## 🛠️ Setup Instructions

### 1. **Run Setup Script**
```bash
node scripts/setupCommandLogging.js
```

This will:
- Create a webhook in the target channel
- Update your `.env` file with the webhook URL
- Send a test message to confirm it's working

### 2. **Manual Setup (Alternative)**

If you prefer manual setup:

1. **Create Webhook**
   - Go to channel `1386343803773915210`
   - Channel Settings → Integrations → Webhooks
   - Create New Webhook named "Command Logger"
   - Copy the webhook URL

2. **Update Environment**
   - Add to your `.env` file:
   ```env
   COMMAND_LOG_WEBHOOK_URL=https://discord.com/api/webhooks/YOUR_WEBHOOK_URL_HERE
   ```

3. **Restart Bot**
   - Restart your bot to load the new environment variable

## 📁 File Structure

```
services/
├── commandLogger.js          # Main logging service
scripts/
├── setupCommandLogging.js    # Automated setup script
events/
├── interactionCreate.js      # Slash command logging integration
├── messageCreate.js          # Message command logging integration
docs/
├── COMMAND_LOGGING.md        # This documentation
```

## 🔧 Configuration

### Environment Variables
```env
COMMAND_LOG_WEBHOOK_URL=https://discord.com/api/webhooks/ID/TOKEN
```

### Customization Options

You can modify `services/commandLogger.js` to:
- Change embed colors
- Add/remove logged fields
- Modify webhook settings
- Add filtering for specific commands

## 📊 Example Log Output

### Slash Command Log
```
🔹 Slash Command Used
/setup arise_crossover

👤 User Information
User: JohnDoe#1234 (123456789012345678)
Display Name: John
Account Created: 2 years ago
Top Roles: Admin, Moderator, VIP

🏠 Server Information  
Server: My Discord Server
Server ID: 987654321098765432
Members: 1,250
Owner: @ServerOwner
Created: 3 years ago

📊 Command Details
Command: /setup
Channel: #bot-commands
Timestamp: December 22, 2024 at 3:45 PM
Options: feature:arise_crossover
```

### Message Command Log
```
💬 Message Command Used
!ping test argument

👤 User Information
User: JaneDoe#5678 (876543210987654321)
Display Name: Jane
Account Created: 1 year ago
Top Roles: Member, Active

🏠 Server Information
Server: My Discord Server
Server ID: 987654321098765432
Members: 1,250
Owner: @ServerOwner
Created: 3 years ago

📊 Command Details
Command: ping
Arguments: test argument
Channel: #general
Message ID: 1234567890123456789
Timestamp: December 22, 2024 at 3:46 PM
```

## 🛡️ Security & Privacy

- **No Sensitive Data**: Only logs command names and public user info
- **Webhook Security**: Uses Discord's secure webhook system
- **Error Handling**: Graceful failure if webhook is unavailable
- **Rate Limiting**: Built-in Discord rate limit handling

## 🔍 Troubleshooting

### Common Issues

1. **No logs appearing**
   - Check if `COMMAND_LOG_WEBHOOK_URL` is set in `.env`
   - Verify webhook URL is correct
   - Restart bot after adding environment variable

2. **Webhook errors**
   - Ensure webhook still exists in Discord
   - Check if webhook token is still valid
   - Verify bot has access to the target channel

3. **Missing information**
   - Some fields may show "Unknown" if bot lacks permissions
   - User roles require bot to have role viewing permissions

### Debug Mode

Add this to see logging status:
```javascript
console.log('Command Logger Status:', commandLogger.webhook ? 'Active' : 'Inactive');
```

## 🚀 Advanced Usage

### Custom Filtering
```javascript
// In commandLogger.js, add filtering logic
if (commandName === 'ping') return; // Skip logging ping commands
```

### Additional Context
```javascript
// Add custom fields to embeds
embed.addFields([
    {
        name: 'Custom Field',
        value: 'Custom Value',
        inline: true
    }
]);
```

## 📈 Benefits

- **📊 Usage Analytics** - Track which commands are used most
- **🐛 Error Monitoring** - Quickly identify and fix issues  
- **👥 User Insights** - Understand how users interact with your bot
- **🔍 Debugging** - Detailed context for troubleshooting
- **📋 Audit Trail** - Complete record of bot interactions

---

**Need help?** Check the console logs for detailed error messages or create an issue in the repository.
